import { Link } from "expo-router";
import { TouchableOpacity, View } from "react-native";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
} from "~/components/ui/card";
import { Text } from "~/components/ui/text";
import { ClassDetailsResponse } from "~/modules/classes/types";
import { obtainDateFrame } from "~/modules/classes/utils";
import MaterialIcons from "@expo/vector-icons/MaterialIcons";

export function ClassCard({
  name,
  start_time,
  end_time,
  id,
  room_name,
  reservation_count,
  is_class_subbed,
  subbing_instructor,
  cancelled,
}: ClassDetailsResponse) {
  return (
    <Link
      push
      href={{
        pathname: "/(classes)/[id]/",
        params: { id, start_time, end_time, name, room_name },
      }}
      asChild
    >
      <TouchableOpacity>
        <Card
          className={`w-full mb-3 border-blue-400 dark:border-white ${
            is_class_subbed
              ? "bg-gray-100 dark:bg-gray-800"
              : cancelled
              ? "bg-red-100 dark:bg-red-900 border-red-400 dark:border-red-500"
              : ""
          }`}
        >
          <CardHeader className="pb-1 pt-4">
            <CardDescription>
              {obtainDateFrame(start_time, end_time)}
            </CardDescription>
          </CardHeader>
          <CardContent className="pb-3">
            {is_class_subbed ? (
              <>
                <Text className="font-bold">(Sub Found) {name}</Text>
                {subbing_instructor && (
                  <View className="flex flex-row items-center">
                    <MaterialIcons
                      name="swap-calls"
                      size={14}
                      color="#069CC3"
                    />
                    <Text className="pl-1 text-sm text-gray-500 dark:text-gray-400">
                      {subbing_instructor}
                    </Text>
                  </View>
                )}
              </>
            ) : (
              <Text className="font-bold">
                <Text>
                  {name}{" "}
                  {cancelled && (
                    <Text className="text-red-500">(Cancelled)</Text>
                  )}
                </Text>
              </Text>
            )}

            {Boolean(reservation_count) && (
              <View className="flex flex-row justify-between">
                <Text>No. of Reservations</Text>
                <Text className="text-[#FF9800] font-bold dark:text-white">
                  {reservation_count}
                </Text>
              </View>
            )}
          </CardContent>
        </Card>
      </TouchableOpacity>
    </Link>
  );
}
