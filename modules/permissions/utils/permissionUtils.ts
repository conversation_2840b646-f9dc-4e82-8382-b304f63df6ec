/**
 * Utility functions for common permission patterns
 */

import { PERMISSIONS, PermissionString } from "../types";

/**
 * Common permission groups for easier management
 */
export const PERMISSION_GROUPS = {
  // All class management permissions
  CLASS_MANAGEMENT: [
    PERMISSIONS.CLASSES.CANCEL_CLASS.ACCESS,
    PERMISSIONS.CLASSES.CONTACT_CLASS.ACCESS,
  ] as PermissionString[],
  
  // All cancel class permissions
  CANCEL_CLASS_FULL: [
    PERMISSIONS.CLASSES.CANCEL_CLASS.ACCESS,
    PERMISSIONS.CLASSES.CANCEL_CLASS.CANCEL,
    PERMISSIONS.CLASSES.CANCEL_CLASS.UPDATE,
    PERMISSIONS.CLASSES.CANCEL_CLASS.DELETE,
  ] as PermissionString[],
  
  // All contact class permissions
  CONTACT_CLASS_FULL: [
    PERMISSIONS.CLASSES.CONTACT_CLASS.ACCESS,
    PERMISSIONS.CLASSES.CONTACT_CLASS.CREATE,
    PERMISSIONS.CLASSES.CONTACT_CLASS.DELETE,
  ] as PermissionString[],
  
  // All appointment permissions
  APPOINTMENT_MANAGEMENT: [
    PERMISSIONS.APPOINTMENTS.CREATE,
    PERMISSIONS.APPOINTMENTS.UPDATE,
    PERMISSIONS.APPOINTMENTS.DELETE,
  ] as PermissionString[],
  
  // All member permissions
  MEMBER_MANAGEMENT: [
    PERMISSIONS.MEMBERS.VIEW,
    PERMISSIONS.MEMBERS.CREATE,
    PERMISSIONS.MEMBERS.UPDATE,
  ] as PermissionString[],
} as const;

/**
 * Helper function to check if user has admin-level permissions
 * (This is just an example - adjust based on your business logic)
 */
export const isAdminUser = (hasPermission: (permission: PermissionString | PermissionString[]) => boolean): boolean => {
  return hasPermission(PERMISSION_GROUPS.CANCEL_CLASS_FULL, { requireAll: true }) &&
         hasPermission(PERMISSION_GROUPS.CONTACT_CLASS_FULL, { requireAll: true });
};

/**
 * Helper function to check if user has basic class management access
 */
export const hasClassManagementAccess = (hasPermission: (permission: PermissionString | PermissionString[]) => boolean): boolean => {
  return hasPermission(PERMISSION_GROUPS.CLASS_MANAGEMENT);
};

/**
 * Helper function to check if user can perform destructive actions
 */
export const canPerformDestructiveActions = (hasPermission: (permission: PermissionString | PermissionString[]) => boolean): boolean => {
  return hasPermission([
    PERMISSIONS.CLASSES.CANCEL_CLASS.DELETE,
    PERMISSIONS.CLASSES.CONTACT_CLASS.DELETE,
  ]);
};

/**
 * Helper function to get user's permission level for a specific feature
 */
export const getPermissionLevel = (
  feature: 'cancel_class' | 'contact_class' | 'appointments' | 'members',
  hasPermission: (permission: PermissionString | PermissionString[]) => boolean
): 'none' | 'read' | 'write' | 'admin' => {
  
  const permissionMaps = {
    cancel_class: {
      access: PERMISSIONS.CLASSES.CANCEL_CLASS.ACCESS,
      create: PERMISSIONS.CLASSES.CANCEL_CLASS.CANCEL,
      update: PERMISSIONS.CLASSES.CANCEL_CLASS.UPDATE,
      delete: PERMISSIONS.CLASSES.CANCEL_CLASS.DELETE,
    },
    contact_class: {
      access: PERMISSIONS.CLASSES.CONTACT_CLASS.ACCESS,
      create: PERMISSIONS.CLASSES.CONTACT_CLASS.CREATE,
      update: PERMISSIONS.CLASSES.CONTACT_CLASS.CREATE, // Assuming create covers update
      delete: PERMISSIONS.CLASSES.CONTACT_CLASS.DELETE,
    },
    appointments: {
      access: PERMISSIONS.APPOINTMENTS.CREATE, // Assuming create implies access
      create: PERMISSIONS.APPOINTMENTS.CREATE,
      update: PERMISSIONS.APPOINTMENTS.UPDATE,
      delete: PERMISSIONS.APPOINTMENTS.DELETE,
    },
    members: {
      access: PERMISSIONS.MEMBERS.VIEW,
      create: PERMISSIONS.MEMBERS.CREATE,
      update: PERMISSIONS.MEMBERS.UPDATE,
      delete: PERMISSIONS.MEMBERS.UPDATE, // Assuming update covers delete for members
    },
  };
  
  const perms = permissionMaps[feature];
  
  if (!hasPermission(perms.access)) {
    return 'none';
  }
  
  if (hasPermission([perms.create, perms.update, perms.delete], { requireAll: true })) {
    return 'admin';
  }
  
  if (hasPermission([perms.create, perms.update])) {
    return 'write';
  }
  
  return 'read';
};

/**
 * Helper function to format permission strings for display
 */
export const formatPermissionForDisplay = (permission: PermissionString): string => {
  const parts = permission.split('.');
  if (parts.length >= 2) {
    const resource = parts[0];
    const action = parts[parts.length - 1];
    
    // Capitalize and format
    const formattedResource = resource.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
    const formattedAction = action.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
    
    return `${formattedResource} - ${formattedAction}`;
  }
  
  return permission;
};

/**
 * Helper function to get all permissions for a specific resource
 */
export const getResourcePermissions = (resource: 'classes' | 'appointments' | 'members'): PermissionString[] => {
  switch (resource) {
    case 'classes':
      return [
        ...Object.values(PERMISSIONS.CLASSES.CANCEL_CLASS),
        ...Object.values(PERMISSIONS.CLASSES.CONTACT_CLASS),
      ] as PermissionString[];
    case 'appointments':
      return Object.values(PERMISSIONS.APPOINTMENTS) as PermissionString[];
    case 'members':
      return Object.values(PERMISSIONS.MEMBERS) as PermissionString[];
    default:
      return [];
  }
};

/**
 * Helper function to validate permission strings
 */
export const isValidPermission = (permission: string): permission is PermissionString => {
  const allPermissions = [
    ...Object.values(PERMISSIONS.CLASSES.CANCEL_CLASS),
    ...Object.values(PERMISSIONS.CLASSES.CONTACT_CLASS),
    ...Object.values(PERMISSIONS.APPOINTMENTS),
    ...Object.values(PERMISSIONS.MEMBERS),
  ];
  
  return allPermissions.includes(permission as PermissionString);
};

/**
 * Helper function for debugging - lists all available permissions
 */
export const getAllPermissions = (): PermissionString[] => {
  return [
    ...Object.values(PERMISSIONS.CLASSES.CANCEL_CLASS),
    ...Object.values(PERMISSIONS.CLASSES.CONTACT_CLASS),
    ...Object.values(PERMISSIONS.APPOINTMENTS),
    ...Object.values(PERMISSIONS.MEMBERS),
  ] as PermissionString[];
};

/**
 * Helper function to create permission-based navigation items
 */
export interface NavigationItem {
  title: string;
  route: string;
  permission: PermissionString;
  icon?: string;
}

export const createPermissionBasedNavigation = (
  items: NavigationItem[],
  hasPermission: (permission: PermissionString) => boolean
): NavigationItem[] => {
  return items.filter(item => hasPermission(item.permission));
};

/**
 * Example usage of permission-based navigation
 */
export const getClassManagementNavigation = (): NavigationItem[] => [
  {
    title: "Contact Classes",
    route: "/(classes)/(tabs)/contact",
    permission: PERMISSIONS.CLASSES.CONTACT_CLASS.ACCESS,
    icon: "contact-class",
  },
  {
    title: "Cancel Classes", 
    route: "/(classes)/(tabs)/cancelled",
    permission: PERMISSIONS.CLASSES.CANCEL_CLASS.ACCESS,
    icon: "cancelled-class",
  },
];

/**
 * Helper to create permission-based action menus
 */
export interface ActionItem {
  title: string;
  action: () => void;
  permission: PermissionString;
  destructive?: boolean;
}

export const createPermissionBasedActions = (
  actions: ActionItem[],
  hasPermission: (permission: PermissionString) => boolean
): ActionItem[] => {
  return actions.filter(action => hasPermission(action.permission));
};
