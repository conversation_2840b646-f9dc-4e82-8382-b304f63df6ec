export type Permission = string[];

export const PERMISSIONS = {
  CANCEL_CLASSES: [
    "classes.cancel_class.access",
    "classes.cancel_class.cancel",
    "classes.cancel_class.update",
    "classes.cancel_class.delete",
  ],
  CONTACT_CLASS: [
    "classes.contact_class.access",
    "classes.contact_class.create",
    "classes.contact_class.delete",
  ],
};

// Permission check options
export interface PermissionCheckOptions {
  requireAll?: boolean; // If true, all permissions must be granted (AND logic)
  fallback?: boolean; // Default value when permission is not found
}

// Permission context type
export interface PermissionContextType {
  permissions: Permission[];
  isLoading: boolean;
  error: Error | null;
  hasPermission: (
    permission: string[],
    options?: PermissionCheckOptions
  ) => boolean;
  refetch: () => void;
}
