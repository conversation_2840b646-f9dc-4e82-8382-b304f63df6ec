/**
 * Permission system exports
 *
 * This module provides a comprehensive permission system for feature flags
 * and access control throughout the application.
 */

// Core types and constants
export * from "./types";

// Context provider and hooks
export {
  PermissionProvider,
  usePermissions,
  usePermission,
} from "./PermissionProvider";

// Declarative components
export { PermissionGate, withPermission } from "./components/PermissionGate";
export { PermissionButton } from "./components/PermissionButton";

// API hooks
export { useUserPermissions } from "./queries/useUserPermissions";

// Utility functions and helpers
export * from "./utils/permissionUtils";

// Re-export commonly used permissions for convenience
export { PERMISSIONS } from "./types";
