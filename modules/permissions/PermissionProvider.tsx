import React, { createContext, useContext, useMemo } from "react";
import { useUserPermissions } from "./queries/useUserPermissions";
import {
  PermissionContextType,
  PermissionCheckOptions,
  Permission,
} from "./types";

const PermissionContext = createContext<PermissionContextType | undefined>(
  undefined
);

interface PermissionProviderProps {
  children: React.ReactNode;
}

export const PermissionProvider: React.FC<PermissionProviderProps> = ({
  children,
}) => {
  const {
    data: permissions = [],
    isLoading,
    error,
    refetch,
  } = useUserPermissions();

  const hasPermission = useMemo(() => {
    return (
      permission: Permission[],
      options: PermissionCheckOptions = {}
    ): boolean => {
      const { requireAll = false, fallback = false } = options;

      // Handle array of permissions
      if (Array.isArray(permission)) {
        if (permission.length === 0) return fallback;

        if (requireAll) {
          // ALL permissions must be granted (AND logic)
          return permission.every((perm) => {
            const found = permissions.find((p) => p === perm);
            return found ? found.granted : fallback;
          });
        } else {
          // ANY permission can be granted (OR logic)
          return permission.some((perm) => {
            const found = permissions.find(
              (p) => `${p.resource}.${p.action}` === perm
            );
            return found ? found.granted : fallback;
          });
        }
      }

      // Handle single permission
      const found = permissions.find(
        (p) => `${p.resource}.${p.action}` === permission
      );
      return found ? found.granted : fallback;
    };
  }, [permissions]);

  const contextValue: PermissionContextType = {
    permissions,
    isLoading,
    error: error as Error | null,
    hasPermission,
    refetch,
  };

  return (
    <PermissionContext.Provider value={contextValue}>
      {children}
    </PermissionContext.Provider>
  );
};

/**
 * Hook to access permission context
 * @throws Error if used outside PermissionProvider
 */
export const usePermissions = (): PermissionContextType => {
  const context = useContext(PermissionContext);

  if (context === undefined) {
    throw new Error("usePermissions must be used within a PermissionProvider");
  }

  return context;
};

/**
 * Convenience hook for checking a specific permission
 * @param permission - Permission string or array to check
 * @param options - Permission check options
 * @returns boolean indicating if permission is granted
 */
export const usePermission = (
  permission: PermissionString | PermissionString[],
  options?: PermissionCheckOptions
): boolean => {
  const { hasPermission } = usePermissions();
  return hasPermission(permission, options);
};
