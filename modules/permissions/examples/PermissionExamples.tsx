/**
 * Comprehensive examples of the Permission System usage
 * 
 * This file demonstrates various ways to use the permission system
 * in different scenarios. Use these patterns in your components.
 */

import React from "react";
import { View, Alert } from "react-native";
import { Text } from "~/components/ui/text";
import { Button } from "~/components/ui/button";
import { 
  PermissionGate, 
  PermissionButton, 
  usePermission, 
  usePermissions,
  withPermission,
  PERMISSIONS 
} from "../index";

// Example 1: Basic Permission Gate
export const BasicPermissionExample = () => {
  return (
    <View>
      <Text>Basic Permission Gate Examples:</Text>
      
      {/* Hide component completely when no permission */}
      <PermissionGate permission={PERMISSIONS.CLASSES.CANCEL_CLASS.ACCESS}>
        <Button label="Cancel Class" onPress={() => Alert.alert("Cancel")} />
      </PermissionGate>
      
      {/* Show fallback when no permission */}
      <PermissionGate 
        permission={PERMISSIONS.CLASSES.CONTACT_CLASS.CREATE}
        fallback={<Text>You don't have permission to create contact classes</Text>}
      >
        <Button label="Create Contact" onPress={() => Alert.alert("Create")} />
      </PermissionGate>
    </View>
  );
};

// Example 2: Multiple Permissions
export const MultiplePermissionExample = () => {
  return (
    <View>
      <Text>Multiple Permission Examples:</Text>
      
      {/* OR Logic (default) - User needs ANY of these permissions */}
      <PermissionGate permission={[
        PERMISSIONS.CLASSES.CANCEL_CLASS.ACCESS,
        PERMISSIONS.CLASSES.CONTACT_CLASS.ACCESS
      ]}>
        <Button label="Class Management" onPress={() => Alert.alert("Manage")} />
      </PermissionGate>
      
      {/* AND Logic - User needs ALL of these permissions */}
      <PermissionGate 
        permission={[
          PERMISSIONS.CLASSES.CANCEL_CLASS.ACCESS,
          PERMISSIONS.CLASSES.CANCEL_CLASS.DELETE
        ]}
        options={{ requireAll: true }}
      >
        <Button label="Delete Cancelled Class" onPress={() => Alert.alert("Delete")} />
      </PermissionGate>
    </View>
  );
};

// Example 3: Permission Hooks
export const PermissionHookExample = () => {
  // Single permission check
  const canCancel = usePermission(PERMISSIONS.CLASSES.CANCEL_CLASS.CANCEL);
  
  // Multiple permissions with options
  const canManageClasses = usePermission([
    PERMISSIONS.CLASSES.CANCEL_CLASS.ACCESS,
    PERMISSIONS.CLASSES.CONTACT_CLASS.ACCESS
  ], { requireAll: false }); // OR logic
  
  // Access full permission context
  const { permissions, isLoading, hasPermission } = usePermissions();
  
  if (isLoading) {
    return <Text>Loading permissions...</Text>;
  }
  
  return (
    <View>
      <Text>Permission Hook Examples:</Text>
      
      {canCancel && (
        <Button label="Cancel Class" onPress={() => Alert.alert("Cancel")} />
      )}
      
      {canManageClasses && (
        <Button label="Manage Classes" onPress={() => Alert.alert("Manage")} />
      )}
      
      <Text>Total permissions: {permissions.length}</Text>
      
      {/* Dynamic permission check */}
      <Button 
        label="Check Dynamic Permission"
        onPress={() => {
          const hasAccess = hasPermission(PERMISSIONS.MEMBERS.VIEW);
          Alert.alert(hasAccess ? "Access granted" : "Access denied");
        }}
      />
    </View>
  );
};

// Example 4: Permission Button
export const PermissionButtonExample = () => {
  return (
    <View>
      <Text>Permission Button Examples:</Text>
      
      {/* Button that disables when no permission */}
      <PermissionButton
        permission={PERMISSIONS.CLASSES.CONTACT_CLASS.CREATE}
        label="Create Contact"
        onPress={() => Alert.alert("Creating contact class")}
        buttonProps={{ className: "bg-blue-500" }}
      />
      
      {/* Button that hides when no permission */}
      <PermissionButton
        permission={PERMISSIONS.CLASSES.CANCEL_CLASS.DELETE}
        label="Delete"
        onPress={() => Alert.alert("Deleting")}
        buttonProps={{ className: "bg-red-500" }}
        hideWhenNoPermission
        disabledMessage="You cannot delete this item"
      />
      
      {/* Button with multiple permissions */}
      <PermissionButton
        permission={[
          PERMISSIONS.CLASSES.CANCEL_CLASS.UPDATE,
          PERMISSIONS.CLASSES.CANCEL_CLASS.DELETE
        ]}
        options={{ requireAll: true }}
        label="Advanced Edit"
        onPress={() => Alert.alert("Advanced editing")}
      />
    </View>
  );
};

// Example 5: Higher-Order Component
const ProtectedComponent = ({ title }: { title: string }) => (
  <View>
    <Text>{title}</Text>
    <Button label="Protected Action" onPress={() => Alert.alert("Action")} />
  </View>
);

const ProtectedWithPermission = withPermission(
  ProtectedComponent,
  PERMISSIONS.CLASSES.CANCEL_CLASS.ACCESS,
  { fallback: false },
  <Text>Access denied to this component</Text>
);

export const HOCExample = () => {
  return (
    <View>
      <Text>Higher-Order Component Example:</Text>
      <ProtectedWithPermission title="Protected Feature" />
    </View>
  );
};

// Example 6: Complex Permission Logic
export const ComplexPermissionExample = () => {
  const { hasPermission } = usePermissions();
  
  // Complex business logic
  const canPerformAction = () => {
    const hasBasicAccess = hasPermission(PERMISSIONS.CLASSES.CANCEL_CLASS.ACCESS);
    const canCancel = hasPermission(PERMISSIONS.CLASSES.CANCEL_CLASS.CANCEL);
    const canUpdate = hasPermission(PERMISSIONS.CLASSES.CANCEL_CLASS.UPDATE);
    
    // Custom logic: can perform action if has access AND (can cancel OR can update)
    return hasBasicAccess && (canCancel || canUpdate);
  };
  
  return (
    <View>
      <Text>Complex Permission Logic:</Text>
      
      {canPerformAction() && (
        <Button 
          label="Complex Action" 
          onPress={() => Alert.alert("Complex action performed")} 
        />
      )}
      
      {/* Nested permission gates */}
      <PermissionGate permission={PERMISSIONS.CLASSES.CANCEL_CLASS.ACCESS}>
        <View>
          <Text>You have basic access</Text>
          
          <PermissionGate permission={PERMISSIONS.CLASSES.CANCEL_CLASS.CANCEL}>
            <Button label="Cancel" onPress={() => Alert.alert("Cancel")} />
          </PermissionGate>
          
          <PermissionGate permission={PERMISSIONS.CLASSES.CANCEL_CLASS.UPDATE}>
            <Button label="Update" onPress={() => Alert.alert("Update")} />
          </PermissionGate>
        </View>
      </PermissionGate>
    </View>
  );
};

// Example 7: Navigation/Menu Items
export const NavigationExample = () => {
  return (
    <View>
      <Text>Navigation Permission Examples:</Text>
      
      {/* Menu items that hide based on permissions */}
      <PermissionGate permission={PERMISSIONS.CLASSES.CONTACT_CLASS.ACCESS}>
        <Button 
          label="📞 Contact Classes" 
          onPress={() => Alert.alert("Navigate to Contact Classes")} 
        />
      </PermissionGate>
      
      <PermissionGate permission={PERMISSIONS.CLASSES.CANCEL_CLASS.ACCESS}>
        <Button 
          label="❌ Cancel Classes" 
          onPress={() => Alert.alert("Navigate to Cancel Classes")} 
        />
      </PermissionGate>
      
      <PermissionGate permission={PERMISSIONS.APPOINTMENTS.CREATE}>
        <Button 
          label="📅 Create Appointment" 
          onPress={() => Alert.alert("Navigate to Create Appointment")} 
        />
      </PermissionGate>
    </View>
  );
};

// Example 8: Form Field Permissions
export const FormPermissionExample = () => {
  return (
    <View>
      <Text>Form Permission Examples:</Text>
      
      {/* Always show field, but disable based on permission */}
      <PermissionGate 
        permission={PERMISSIONS.CLASSES.CANCEL_CLASS.UPDATE}
        fallback={
          <View>
            <Text>Reason (Read-only):</Text>
            <Text>You cannot edit this field</Text>
          </View>
        }
      >
        <View>
          <Text>Reason (Editable):</Text>
          <Button label="Edit Reason" onPress={() => Alert.alert("Edit")} />
        </View>
      </PermissionGate>
      
      {/* Conditional form sections */}
      <PermissionGate permission={PERMISSIONS.CLASSES.CANCEL_CLASS.DELETE}>
        <View>
          <Text>Danger Zone:</Text>
          <Button 
            label="Delete Class" 
            onPress={() => Alert.alert("Delete")}
          />
        </View>
      </PermissionGate>
    </View>
  );
};

// Main example component showcasing all patterns
export const AllPermissionExamples = () => {
  return (
    <View style={{ padding: 20 }}>
      <Text style={{ fontSize: 24, fontWeight: 'bold', marginBottom: 20 }}>
        Permission System Examples
      </Text>
      
      <BasicPermissionExample />
      <MultiplePermissionExample />
      <PermissionHookExample />
      <PermissionButtonExample />
      <HOCExample />
      <ComplexPermissionExample />
      <NavigationExample />
      <FormPermissionExample />
    </View>
  );
};
