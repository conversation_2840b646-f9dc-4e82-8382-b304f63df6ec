import { useQuery } from "@tanstack/react-query";
import { api } from "~/lib/api";

const fetchUserPermissions = async () => {
  try {
    const response = await api
      .get<{ data: { role: { permissions: string[] } } }>("/user/permissions")
      .json();
    return response.data;
  } catch (error) {
    console.error("Failed to fetch user permissions:", error);
    throw new Error("Unable to fetch user permissions");
  }
};

export const useUserPermissions = () => {
  return useQuery({
    queryKey: ["user", "permissions"],
    queryFn: fetchUserPermissions,
    select: (data) => data.role?.permissions || [],
  });
};
